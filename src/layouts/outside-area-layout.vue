<script lang="ts">
import { defineComponent, ref, watch, nextTick, computed } from 'vue';
import { AuthSessionApi } from '@/engines/login/apis/auth/session.api';
import { useRoute, useRouter } from 'vue-router';
import { useToastProvide } from '@/components/global/ta-component/TaIndexView/ta-index-view-core/useToast';
import ComAdaptiveView from './ComAdaptiveView.vue';
import scrollIntoView from 'smooth-scroll-into-view-if-needed';
import { CaretDownFilled } from '@ant-design/icons-vue';
import { useStore } from 'vuex';
import ResUserInfoShow from '@/components/user/ComUserInfo.vue';
import InnoUserSettingsIndex from '@/views/setting/Index.vue';

const OutsideAreaLayout = defineComponent({
  name: 'OutsideAreaLayout',
  components: {
    ComAdaptiveView,
    CaretDownFilled,
    ResUserInfoShow,
    InnoUserSettingsIndex,
  },
  setup() {
    // const { fetchMenuTree } = useAutoMenu();
    const locked = ref(true);
    if (AuthSessionApi.token()) {
      locked.value = false;
    }

    const opacity = ref(0);
    const route = useRoute();

    const getScroll = (event: any) => {
      if (
        event.target.scrollTop / ((route.meta.opacityTriggerHeight as undefined | number) || 750) <
        1
      ) {
        opacity.value = event.target.scrollTop / 700;
      }
    };

    useToastProvide();

    const isKeepAlive = ref(false);

    watch(
      () => route.meta,
      () => {
        isKeepAlive.value = !!route.meta?.isKeepAlive;
      },
      {
        deep: true,
      },
    );

    const renderedKeys: string[] = [];

    const getBindingKey = (args: any) => {
      const result = args.Component.type.name.includes('Switch')
        ? {}
        : {
            key:
              args.route.meta.keepAlive === false
                ? `${args.route.fullPath}-${new Date().getTime()}`
                : args.route.fullPath,
          };

      if (renderedKeys.findIndex(i => i === result.key) === -1) {
        renderedKeys.push(result.key);
        onEnter();
      }

      return result;
    };

    const topContainer = ref<any>(null);
    const onEnter = () => {
      nextTick(() => {
        if (topContainer.value) {
          scrollIntoView(topContainer.value, {
            scrollMode: topContainer.value,
            block: 'top',
            behavior: 'smooth',
          });
        }
      });
    };

    const logout = async () => {
      await new AuthSessionApi().logout().then(() => {
        const domain = window.location.host;
        window.open(`//${domain}${process.env.VUE_APP_PUBLIC_PATH}login`, '_self');
      });
    };

    const currentUser = computed(() => AuthSessionApi.currentUser());

    const store = useStore();
    const isLoading = computed(() => {
      console.log('store.state.app.isLoading', store.state.app.isLoading);
      return store.state.app.isLoading;
    });

    const router = useRouter();
    const checkIndex = () => {
      router.push(`${process.env.VUE_APP_HOME_PATH}`);
    };
    const userInfo = ref();
    const checkUser = () => {
      // router.push('/user/info');
      userInfo.value?.openUserInfo();
    };

    const visibleFundSetting = ref(false);

    return {
      getScroll,
      opacity,
      isKeepAlive,
      getBindingKey,
      onEnter,
      topContainer,
      logout,
      currentUser,
      isLoading,
      checkIndex,
      checkUser,
      userInfo,
      visibleFundSetting,
    };
  },
});

export default OutsideAreaLayout;
</script>

<template lang="pug">
.outside-area-layout#layout.h-screen.flex-col.flex-center.w-full.default-bg
  //- ComAdaptiveView(:maxWidth='1440', :minWidth='380')
  .tabs.bg-white.flex.flex-row.fixed.top-0.left-0.right-0.z-10
    .h-15.w-full.flex.flex.justify-between.items-center.px-6.py-3
      img.h-11.w-30.cursor-pointer(src='https://oss.innomatch.net/static-assets/crc/ertongbarlogo.png' @click='checkIndex')
      a-popover(placement='bottomRight')
        template(#content)
          .flex.flex-col.rounded
            .px-3.py-2.cursor-pointer.text-xs.color_text.rounded(@click='visibleFundSetting = true' class='hover:bg-blue-50') 经费设置
            .px-3.py-2.cursor-pointer.text-xs.color_text.rounded(@click='checkUser' class='hover:bg-blue-50') 个人信息
            .px-3.py-2.cursor-pointer.text-xs.color_text.rounded(@click='logout' class='hover:bg-blue-50') 退出登录
        .flex.flex-row.items-center.cursor-pointer
          TaAvatar.cursor-pointer.mr-2(:user='currentUser' :size='32')
          .text-base.text-gray-900.cursor-pointer.mr-1 {{ currentUser?.name  }}
          TaIcon(type='CaretDownFilled')
  a-spin.w-full(:spinning='isLoading' size="large" wrapperClassName='crc_spin_wrapper')
    .h-full.flex-grow.bg-white.pt-15(class='!w-full')
      router-view(v-slot='args')
        .w-full.content-container.overflow-y-auto.h-full.relative(class='!w-full', @scroll.passive="getScroll")
          .w-full.adaptive-content.flex.flex-col.items-center.min-h-full
            .top(ref='topContainer')
            transition-group(name='fade', mode='in-out')
              component.w-full.flex-grow(:is='args.Component', v-bind='getBindingKey(args)')
  TaToasts
  ResUserInfoShow(ref='userInfo')
  TaNoPaddingModal(
    v-if='visibleFundSetting',
    v-model:visible='visibleFundSetting',
    title='经费设置',
    width='1100px',
    :modalContentStyle='{ "border-radius": "12px", overflow: "hidden" }',
    :footer='null'
  )
    InnoUserSettingsIndex.h-full

</template>

<style lang="stylus" scoped>
.main
  width 100%
  background #ffffff
  height 100vh

.fade-enter-active, .fade-leave-active
  transition: opacity 0.3s ease-in-out !important

.fade-enter-from, .fade-leave-to
  opacity: 0 !important
.main-container
  width fit-content
.content-container
  // height calc(100% - 58px)
  height 100%
.adaptive-content
  min-width $screen-width
</style>

<style lang="stylus">
.crc_spin_wrapper
  height 100%
  width 100%
  .ant-spin-container
    height 100%
  .ant-spin-blur
    opacity 1 !important
  .ant-spin-blur::after
    opacity 0.5 !important
  .ant-spin-container::after
    background white !important
</style>
