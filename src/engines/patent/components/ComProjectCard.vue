<script lang="ts">
import { defineComponent, toRefs } from 'vue';
import dayjs from 'dayjs';

const ComProjectCard = defineComponent({
  name: 'ComProjectCard',
  components: {},
  props: {
    record: { type: Object, default: () => {} },
    store: { type: Object, default: () => {} },
    index: { type: Number, default: 0 },
    actions: { type: Object, default: () => ({}) },
  },
  setup(props) {
    return {
      ...toRefs(props),
      dayjs,
    };
  },
});
export default ComProjectCard;
</script>

<template lang="pug">
.com-project-card.py-3.cursor-pointer.space-y-2(:class='{"border-card": index > 0}')
  .flex.items-center.justify-between
    .info-box.flex.items-center
      .text-lg.text_color_1.font-medium {{ record?.name }}
      .ml-6.flex.items-center.gap-2
        .px-10px.py-2px.rounded.text-xs(v-for='item in record.tags', :style='{ "background-color": item.bgColor, color: item.color }') {{ item?.label }}
    .flex.items-center.gap-6
      TaTextButton(icon='EyeOutlined', @click='() => actions.onShow(record)') 查看详情
      TaTextButton(icon='EditOutlined', @click='() => actions.onEdit(record)') 修改信息
      TaPolicyResource(
        actionKey='delete',
        :store='record.store',
        :resource_id='record.id',
      )
        TaPopoverConfirm(
          title='删除',
          content='确认删除该记录吗',
          @click.stop='',
          @confirm='actions.onDelete(record)'
        )
          TaTextButton(icon='DeleteOutlined') 删除
  .flex.items-center.space-x-2.justify-between
    .space-x-4.flex.items-center
      .flex.items-center.space-x-2
        TaIcon(type='FileTextOutlined')
        .text-gray-800 {{ record?.seq }}
      .flex.items-center.space-x-2
        TaIcon(type='UserOutlined')
        .text-gray-800 {{ record?.leader_name }}
      .flex.items-center.space-x-2
        TaIcon(type='CalendarOutlined')
        .text-gray-800 {{ record?.payload_summary?.签约时间 ? dayjs(record?.payload_summary?.签约时间).format('YYYY-MM-DD') : '-' }}
    .space-x-4.flex.items-center
      .flex.items-center.space-x-2
        .text-gray-800 总金额：{{ record.contract_amount ? `${(Number(record.contract_amount)).toLocaleString()}元` : '-' }}
      .flex.items-center.space-x-2
        .text-gray-800 已到账：{{ record.actual_amount ? `${(Number(record.actual_amount)).toLocaleString()}元` : '-' }}
  .flex.items-center.space-x-2
    .text-gray-800 知识产权：
    ComInnoProjectRelationStatistics(:record='record')
  .flex.items-center.space-x-2
    .text-gray-800 合作方：
    a-tag 甲方：{{ record?.model_payload?.受让方 || '-' }}
    a-tag 乙方：{{ record?.model_payload?.让与方 || '-' }}
    a-tag(v-if='record?.model_payload?.中介方') 丙方：{{ record?.model_payload?.中介方 || '-' }}
  .flex.items-center.space-x-2
    ComInnoProjectPayloadStatistics(:record='record')
  .mt-4.flex.flex-wrap.gap-2
    .flex(v-for='item in list', :class='[item?.class], item?.basis ? `flex-basis-${item?.basis}`: "flex-basis-basic"')
      text.text-gray-500.text-sm.flex-shrink-0 {{item?.name}}：
      text.text-gray-900.text-sm {{item?.value}}{{item?.unit}}
</template>

<style lang="stylus" scoped>
.border-card
  border-top: 1px solid #E5E7EB
.flex-basis-basic
  flex-basis: calc(33.3%);
.flex-basis-1
  flex-basis: 1 !important;
</style>
