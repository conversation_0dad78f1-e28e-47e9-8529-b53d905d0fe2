<script lang="ts">
import { ref, defineComponent, toRefs, computed } from 'vue';
import TAnchor from '@/engines/patent/components/Anchor/TAnchor.vue';
import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';
import dayjs from 'dayjs';
import { VStore, VObject } from '@/lib/vails';
import { message } from 'ant-design-vue';
import ComInnoProjectRelationCard from '../../ComInnoProjectRelationCard.vue';
import { InnoUserFundsApi } from '@/engines/patent/patent-core/apis/inno/user/funds.api';
import { InnoFundModel } from '@/engines/patent/patent-core/models/inno/user/funds';
import ComInnoProjectFundsIndex from '../funds/ComInnoProjectFundsIndex.vue';
import { IFile } from '@/components/global/ta-component/file/servers';

export const InnoProjectPayloadSteps = [
  { key: 'key1', label: '评估定价', template: 'inno_project#评估定价' },
  { key: 'key2', label: '签订意向', template: 'inno_project#签订意向' },
  { key: 'key3', label: '上会决策', template: 'inno_project#上会决策' },
  { key: 'key4', label: '挂牌交易', template: 'inno_project#挂牌交易' },
  { key: 'key5', label: '院内公示', template: 'inno_project#院内公示' },
  { key: 'key6', label: '合同签订', template: 'inno_project#合同签订' },
  { key: 'key7', label: '交易凭证', template: 'inno_project#交易凭证' },
  { key: 'key8', label: '合同认定登记', template: 'inno_project#合同认定登记' },
  { key: 'key9', label: '权利变更/许可备案', template: 'inno_project#权利变更' },
];

const ComInnoProjectShow = defineComponent({
  name: 'ComInnoProjectShow',
  components: {
    TAnchor,
    ComInnoProjectRelationCard,
    ComInnoProjectFundsIndex,
  },
  props: {
    store: { type: Object, required: true },
    record: { type: Object, default: () => {} },
  },
  setup(props) {
    usePolicy();

    if (!props.record.formData.payload) {
      props.record.formData.payload = {};
    }

    const fundStore = new VStore(
      new InnoUserFundsApi({ params: { q: { project_id_eq: props.record.id } } }),
      InnoFundModel,
    );

    const anchorList = computed(() => [
      {
        anchor: '#overview',
        text: '项目概览',
      },
      {
        anchor: '#fund_info',
        text: '经费信息',
      },
      {
        anchor: '#flow_info',
        text: '项目流程概览',
      },
      {
        anchor: '#relation_info',
        text: '关联知识产权',
      },
      {
        anchor: '#convert_info',
        text: '阶段详情',
      },
      {
        anchor: '#basic_info',
        text: '基本信息',
        isSub: true,
      },
      ...flowInfoNotEmpty.value.map(item => ({
        anchor: `#${item.key}`,
        text: item.label,
        isSub: true,
      })),
    ]);

    const actions = {
      refresh: () => {
        props.record.fetch();
      },
    };

    const isDataEmpty = (data: any) => {
      return data === null || data === undefined || Object.keys(data).length === 0;
    };

    const flowInfo = computed(() =>
      InnoProjectPayloadSteps.map(step => ({
        key: step.key,
        label: step.label,
        template: step.template,
        isDataEmpty: isDataEmpty(props.record?.payload?.[step.label]),
      })),
    );

    const flowInfoNotEmpty = computed(() => flowInfo.value.filter(item => !item.isDataEmpty));

    const activeItem = ref<VObject | null>(null);
    const visible = ref(false);

    const onShowFlowDetail = (item: VObject) => {
      activeItem.value = item;
      visible.value = true;
    };

    const onConfirm = () => {
      props.record
        .save()
        .then(() => {
          visible.value = false;
          activeItem.value = null;
          actions.refresh();
          message.success('保存成功');
        })
        .catch(() => {
          message.error('保存失败，请稍后重试');
        });
    };

    const visibleEdit = ref(false);
    const onEdit = () => {
      visibleEdit.value = true;
    };

    const visibleFundInfo = ref(false);

    const fileCheckList = computed(() => {
      return [
        {
          label: '合同',
          value: props.record.payload_summary?.合同,
        },
        {
          label: '技术交易凭证',
          value: props.record.payload_summary?.技术交易凭证,
        },
        {
          label: '合同认定证明',
          value: props.record.payload_summary?.合同认定证明,
        },
      ];
    });

    const visibleFilePreview = ref(false);
    const activeFile = ref<IFile | null>(null);
    const onPreview = (files: IFile[]) => {
      if (files?.[0]) {
        activeFile.value = files[0];
        visibleFilePreview.value = true;
      }
    };

    return {
      ...toRefs(props),
      anchorList,
      actions,
      dayjs,
      flowInfo,
      flowInfoNotEmpty,
      onShowFlowDetail,
      activeItem,
      visible,
      onConfirm,
      visibleEdit,
      onEdit,
      onEditConfirm: onConfirm,
      fundStore,
      visibleFundInfo,
      fileCheckList,
      visibleFilePreview,
      activeFile,
      onPreview,
    };
  },
});
export default ComInnoProjectShow;
</script>

<template lang="pug">
.com-inno-project-show.flex.gap-4
  TaFilePreviewer(
    v-if='activeFile && visibleFilePreview'
    v-model:visible='visibleFilePreview',
    :attachment='activeFile'
  )
  TAnchor.left_anchor.h-full.w-148px.flex-shrink-0(
    :anchor-list='anchorList',
    anchor-parent='#wrapper_patent',
    style=''
  )
  #wrapper_patent.right_content.w-full.overflow-y-scroll.space-y-4(
    style='height: calc(100vh - 160px)'
  )
    section#overview.bg-white.rounded-lg.px-6.py-4.w-full.flex.justify-between
      .right.flex-grow.w-0.space-y-4
        .flex.items-center.justify-between
          .info-box.flex.items-center.text-lg
            .text-lg.text_color_1.font-medium {{ record?.name }}
            .ml-6.flex.items-center.gap-2
              .px-10px.py-2px.rounded.text-xs(
                v-for='item in record.tags',
                :style='{ "background-color": item.bgColor, color: item.color }'
              ) {{ item?.label }}
          .actions
            //- TaTextButton(icon='EditOutlined', @click='onEdit') 修改信息
        .flex.items-center.space-x-2.justify-between
          .space-x-4.flex.items-center
            .flex.items-center.space-x-2
              TaIcon(type='FileTextOutlined')
              .text-gray-800 项目编号：{{ record?.seq }}
            .flex.items-center.space-x-2
              TaIcon(type='CalendarOutlined')
              .text-gray-800 签约时间：{{ record?.payload_summary?.签约时间 ? dayjs(record?.payload_summary?.签约时间).format('YYYY-MM-DD') : '-' }}
            .flex.items-center.space-x-2
              TaIcon(type='UserOutlined')
              .text-gray-800 负责人：{{ record?.leader_name }}
            .flex.items-center.space-x-2
              TaIcon(type='CalendarOutlined')
              .text-gray-800 提交时间：{{ record?.created_at ? dayjs(record?.created_at).format('YYYY-MM-DD HH:mm') : '-' }}
            ComInnoProjectRelationStatistics(:record='record')
      .left.flex-shrink-0.space-y-2.p-2
        a-tag(
          v-for='item in fileCheckList',
          :key='item.label',
          :color='(item.value || []).length > 0 ? "green" : undefined'
          :class='(item.value || []).length > 0 ? "cursor-pointer" : "cursor-not-allowed"'
          @click='onPreview(item.value)'
        )
          .flex.items-center
            TaIcon(:type='(item.value || []).length > 0 ? "CheckCircleOutlined" : "ClockCircleOutlined"')
            span.ml-1 {{ item.label }}

    section#fund_info.bg-white.rounded-lg.px-6.py-4.w-full.space-y-4
      .flex.items-center.justify-between
        .info-box.flex.items-center.text-base
          .text_color_1.font-medium 经费信息
        a-button(type='primary', @click='visibleFundInfo = true') 查看付款明细
      .grid.grid-cols-3.gap-2
        .bg-slate-50.rounded-lg.p-3.border.border-slate-200
          .text-xs.text-gray-500.font-medium 项目总金额
          .text-lg.text-gray-800.font-semibold {{ record?.contract_amount ? `${Number(record.contract_amount).toLocaleString()}元` : '-' }}
        .bg-green-50.rounded-lg.p-3.border.border-green-200
          .text-xs.text-gray-500.font-medium 已到账金额
          .text-lg.text-gray-800.font-semibold {{ record?.actual_amount ? `${Number(record.actual_amount).toLocaleString()}元` : '-' }}
        .bg-blue-50.rounded-lg.p-3.border.border-blue-200
          .text-xs.text-gray-500.font-medium 到账进度
          .text-lg.text-gray-800.font-semibold {{ record?.contract_amount ? `${(Number(record.actual_amount) / Number(record.contract_amount) * 100).toFixed(2)}%` : '-' }}
    section#flow_info.bg-white.rounded-lg.px-6.py-4.w-full.space-y-4
      .flex.items-center.justify-between
        .info-box.flex.items-center.text-base
          .text_color_1.font-medium 项目流程概览
      .grid.grid-cols-4.gap-2
        .flow-card.flex.items-center.justify-between.text-primary.bg-blue-gray-50.rounded-lg.p-4(
          v-for='item in flowInfo',
          :key='item.key',
          :class='{ "text-gray-300": item.isDataEmpty }'
        )
          .flex.items-center.gap-2(
            :class='item.isDataEmpty ? "text-primary" :  "text-green-800" ',
          )
            .w-3.h-3.rounded-full(:class='item.isDataEmpty ? "bg-primary" : "bg-green-500"')
            .font-medium {{ item.label }}
          .rounded.text-xs.p-1.cursor-pointer(
            :class='item.isDataEmpty ? "bg-primary text-white" :  "bg-green-700 text-white" ',
            @click='() => onShowFlowDetail(item)',
          )
            | {{ item.isDataEmpty ? '去填写' : '修改' }}

    section#flow_info.bg-white.rounded-lg.px-6.py-4.w-full.space-y-4
      .flex.items-center.justify-between
        .info-box.flex.items-center.text-base
          .text_color_1.font-medium 关联知识产权
      .space-y-2
        template(
          v-for='relation in record?.project_relations',
          :key='relation.id',
        )
          ComInnoProjectRelationCard(
            v-if='relation.source',
            :record='relation'
          )

    section#basic_info.bg-white.rounded-lg.px-6.py-4.w-full.space-y-4
      .flex.items-center.justify-between
        .info-box.flex.items-center.text-base
          .w-3.h-3.bg-primary.mr-2
          .text_color_1.font-medium 基本信息
        .actions
          a-button(type='primary', @click='onEdit') 修改
      TaTemplateFormSimpleViewer(:record='record', template='inno_project#model')

    section#convert_info
    .more-info.pb-10.space-y-4
      section.bg-white.rounded-lg.px-6.py-4.w-full.space-y-4(
        v-for='item in flowInfoNotEmpty',
        :key='item.key',
        :id='item.key'
      )
        .flex.items-center.justify-between
          .info-box.flex.items-center.text-base
            .w-3.h-3.bg-primary.mr-2
            .text_color_1.font-medium {{ item.label }}
          .actions
            a-button(type='primary', @click='() => onShowFlowDetail(item)') 修改
        TaTemplateFormSimpleViewer.flow-info-not-empty-viewer(:record='record', :template='item.template')

    TaTemplateFormModal(
      v-if='visible && activeItem',
      v-model:visible='visible',
      :record='record',
      :title='activeItem.label',
      :template='activeItem?.template'
      width='80vw',
      @confirm='onConfirm'
    )

    TaTemplateFormModal(
      v-if='visibleEdit',
      v-model:visible='visibleEdit'
      :record='record'
      title='修改项目信息'
      template='inno_project#model'
      width='80vw',
      @confirm='onEditConfirm'
    )

    TaNoPaddingModal(
      v-model:visible='visibleFundInfo',
      width='1100px',
      @update:visible='(val) => { if (!val) record.fetch() }',
    )
      template(#title)
        .text-xl.font-semibold.text-gray-900.flex.items-center.gap-2
          TaIcon(:size='18', color='#2563eb', type='CreditCardOutlined')
          .text 付款明细-{{ record?.name }}
      ComInnoProjectFundsIndex(:store='fundStore', :project='record')
</template>

<style lang="stylus" scoped>
.com-inno-project-show
  .more-info
    >>> .simple-viewer-file
      label
        width 8rem
      .value
        @apply truncate
    >>> .simple-viewer-date_range_new
        label
          width 12rem
    >>> .simple-viewer-date
        label
          width 12rem
</style>
